package com.smaile.health.model;

import com.smaile.health.constants.Status;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.time.Instant;
import java.util.List;
import java.util.UUID;

@Getter
@Setter
public class AgreementDTO {

    private UUID id;

    @NotNull
    @NotEmpty
    private String contractId;

    private MedicalProviderSimpleInfoDTO medicalProvider;

    @NotNull
    private String shortDescription;

    private String longDescription;

    @NotNull
    private Instant effectiveTime;

    @NotNull
    private Instant cancellationTime;

    @NotNull
    private List<AgreementProcedureDTO> procedures;

    @NotNull
    private Status status;
}
