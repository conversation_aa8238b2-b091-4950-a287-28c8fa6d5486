package com.smaile.health.domain;

import com.smaile.health.audit.AuditableEntity;
import com.smaile.health.audit.AuditableField;
import com.smaile.health.constants.Status;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

import java.time.Instant;
import java.util.List;
import java.util.UUID;

@Entity
@Table(name = "agreements")
@Getter
@Setter
@AuditableEntity(entityName = "Agreement")
public class Agreement extends BaseEntity {

    @Id
    @Column(nullable = false, updatable = false)
    private UUID id;

    @Column(nullable = false, unique = true)
    @AuditableField(displayName = "Contract ID")
    private String contractId;

    @Column(nullable = false)
    @AuditableField(displayName = "Short Description")
    private String shortDescription;

    @Column(columnDefinition = "TEXT")
    @AuditableField(displayName = "Long Description")
    private String longDescription;

    @Column
    @AuditableField(displayName = "Effective Time")
    private Instant effectiveTime;

    @Column
    @AuditableField(displayName = "Cancellation Time")
    private Instant cancellationTime;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    @AuditableField(displayName = "Status")
    private Status status;

    @ManyToOne
    @JoinColumn(name = "medical_provider_id", nullable = false)
    private MedicalProvider provider;

    @OneToMany(mappedBy = "agreement", cascade = CascadeType.PERSIST, orphanRemoval = true, fetch = FetchType.EAGER)
    private List<AgreementProceduresDetail> proceduresDetails;

    @OneToMany(mappedBy = "agreement", fetch = FetchType.LAZY)
    private List<IcAgreement> icAgreements;

    public static class Fields {

        public static final String ID = "id";
        public static final String CONTRACT_ID = "contractId";
        public static final String SORT_DESCRIPTION = "shortDescription";
        public static final String LONG_DESCRIPTION = "longDescription";
        public static final String EFFECTIVE_TIME = "effectiveTime";
        public static final String CANCELLATION_TIME = "cancellationTime";
        public static final String STATUS = "status";
        public static final String PROVIDER = "provider";
        public static final String PROVIDER_ID = "provider.id";
        public static final String PROCEDURES_DETAILS = "proceduresDetails";

    }

}
