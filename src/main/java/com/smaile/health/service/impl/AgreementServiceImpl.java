package com.smaile.health.service.impl;

import com.smaile.health.constants.ErrorCode;
import com.smaile.health.constants.OrganizationType;
import com.smaile.health.constants.Status;
import com.smaile.health.domain.Agreement;
import com.smaile.health.domain.AgreementProceduresDetail;
import com.smaile.health.domain.AgreementProceduresDetailId;
import com.smaile.health.domain.MedicalProvider;
import com.smaile.health.domain.Procedure;
import com.smaile.health.exception.ValidationException;
import com.smaile.health.mapper.AgreementMapper;
import com.smaile.health.mapper.MedicalProviderMapper;
import com.smaile.health.mapper.ProcedureMapper;
import com.smaile.health.model.AgreementDTO;
import com.smaile.health.model.AgreementProcedureDTO;
import com.smaile.health.model.request.AgreementProcedureRequest;
import com.smaile.health.model.request.CreateAgreementDTO;
import com.smaile.health.model.request.Filter;
import com.smaile.health.model.request.UpdateAgreementDTO;
import com.smaile.health.model.request.UpdateAgreementProcedureRequest;
import com.smaile.health.repository.AgreementProceduresDetailRepository;
import com.smaile.health.repository.AgreementRepository;
import com.smaile.health.repository.MedicalProviderRepository;
import com.smaile.health.repository.ProcedureRepository;
import com.smaile.health.repository.specification.AgreementSpecification;
import com.smaile.health.service.AgreementService;
import com.smaile.health.service.I18nService;
import com.smaile.health.util.PageResponse;
import com.smaile.health.util.SecurityUtils;
import com.smaile.health.util.UUIDv7;
import lombok.AllArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@AllArgsConstructor
public class AgreementServiceImpl implements AgreementService {

    private final I18nService i18nService;

    private final MedicalProviderRepository medicalProviderRepository;
    private final AgreementRepository agreementRepository;
    private final AgreementProceduresDetailRepository agreementProceduresDetailRepository;
    private final ProcedureRepository procedureRepository;
    private final AgreementMapper agreementMapper;
    private final MedicalProviderMapper medicalProviderMapper;
    private final ProcedureMapper procedureMapper;

    @Override
    public PageResponse<AgreementDTO> query(String search, List<Filter> filters, Pageable pageable) {
        OrganizationType type = SecurityUtils.getActorOrganization().get().getType();
        Specification<Agreement> spec = AgreementSpecification
                .search(search)
                .and(AgreementSpecification.withFilter(filters));
        if (Set.of(OrganizationType.IC).contains(type)) {
            spec.and(AgreementSpecification.withChildMedicalProvider(SecurityUtils.getActorOrgId()));
        }
        Page<Agreement> page = agreementRepository.findAll(spec, pageable);
        Page<AgreementDTO> pageResponse = page.map(this::toAgreementDTO);
        return PageResponse.of(pageResponse);
    }

    @Override
    public AgreementDTO detail(UUID agreementId) {
        Agreement agreement = agreementRepository.findById(agreementId).orElseThrow(() -> new ValidationException(
                i18nService.getMessage(ErrorCode.AGREEMENT_NOT_FOUND.getMessageKey())
        ));
        return toAgreementDTO(agreement);
    }

    @Override
    @Transactional
    public AgreementDTO create(CreateAgreementDTO agreementRequest) {
        MedicalProvider provider = medicalProviderRepository.findById(agreementRequest.getProviderId())
                .orElseThrow(() -> new ValidationException(
                                i18nService.getMessage(ErrorCode.MP_NOT_FOUND.getMessageKey())
                        )
                );
        if (agreementRequest.getEffectiveTime().isAfter(agreementRequest.getCancellationTime())) {
            throw new ValidationException(
                    i18nService.getMessage(ErrorCode.TIME_RANGE_INVALID.getMessageKey())
            );
        }
        if (agreementRepository.existsByContractId(agreementRequest.getContractId())) {
            throw new ValidationException(
                    i18nService.getMessage(ErrorCode.CONTRACT_ID_DUPLICATE.getMessageKey())
            );
        }
        if (!provider.getParent().getId().equals(SecurityUtils.getCurrentUserOrganizationId().get())) {
            throw new ValidationException(
                    i18nService.getMessage(ErrorCode.NO_AUTHORIZED_CREATE_AGREEMENT_WITH_MP.getMessageKey(),
                            provider.getName())
            );
        }

        Agreement agreement = toAgreement(agreementRequest);
        agreement.setId(UUIDv7.generate());
        agreement.setProvider(provider);
        agreement.setStatus(Status.ACTIVE);
        agreementRepository.saveAndFlush(agreement);
        if (agreementRequest.getProcedures() != null) {
            List<Procedure> procedures = procedureRepository.findAllById(
                    agreementRequest.getProcedures().stream().map(AgreementProcedureRequest::getProcedureId).toList());
            Map<UUID, Procedure> procedureMap = procedures.stream()
                    .collect(Collectors.toMap(Procedure::getId, Function.identity()));
            agreementRequest.getProcedures().forEach(agreementProcedureRequest -> {
                if (!procedureMap.containsKey(agreementProcedureRequest.getProcedureId())) {
                    throw new ValidationException(
                            i18nService.getMessage(ErrorCode.PROCEDURE_NOT_FOUND.getMessageKey(),
                                    agreementProcedureRequest.getProcedureId())
                    );
                }
            });
            Map<UUID, BigDecimal> procedureCostMap = agreementRequest.getProcedures().stream()
                    .collect(Collectors.toMap(AgreementProcedureRequest::getProcedureId,
                            AgreementProcedureRequest::getCost));
            agreement.setProceduresDetails(toAgreementProceduresDetail(agreement, procedures, procedureCostMap));
            agreementProceduresDetailRepository.saveAllAndFlush(agreement.getProceduresDetails());
        }
        return detail(agreement.getId());
    }

    @Override
    @Transactional
    public AgreementDTO update(UpdateAgreementDTO agreementRequest) {
        Agreement agreement = agreementRepository.findById(agreementRequest.getId())
                .orElseThrow(() -> new ValidationException(
                                i18nService.getMessage(ErrorCode.AGREEMENT_NOT_FOUND.getMessageKey())
                        )
                );
        if (agreementRequest.getEffectiveTime().isAfter(agreementRequest.getCancellationTime())) {
            throw new ValidationException(
                    i18nService.getMessage(ErrorCode.TIME_RANGE_INVALID.getMessageKey())
            );
        }

        List<Agreement> agreementsSameContractId = agreementRepository.findAllByContractId(
                agreementRequest.getContractId());
        agreementsSameContractId.removeIf(a -> a.getId().equals(agreementRequest.getId()));
        if (!agreementsSameContractId.isEmpty()) {
            throw new ValidationException(
                    i18nService.getMessage(ErrorCode.CONTRACT_ID_DUPLICATE.getMessageKey())
            );
        }

        agreement.setContractId(agreementRequest.getContractId());
        agreement.setStatus(agreementRequest.getStatus());
        agreement.setShortDescription(agreementRequest.getShortDescription());
        agreement.setLongDescription(agreementRequest.getLongDescription());
        agreementRepository.save(agreement);
        return detail(agreement.getId());
    }

    @Override
    public AgreementDTO updateProcedure(UpdateAgreementProcedureRequest agreementRequest) {
        Agreement agreement = agreementRepository.findById(agreementRequest.getId())
                .orElseThrow(() -> new ValidationException(
                                i18nService.getMessage(ErrorCode.AGREEMENT_NOT_FOUND.getMessageKey())
                        )
                );

        List<Procedure> procedures = procedureRepository.findAllById(
                agreementRequest.getProcedures().stream().map(AgreementProcedureRequest::getProcedureId).toList());
        Map<UUID, Procedure> procedureMap = procedures.stream()
                .collect(Collectors.toMap(Procedure::getId, Function.identity()));
        agreementRequest.getProcedures().forEach(agreementProcedureRequest -> {
            if (!procedureMap.containsKey(agreementProcedureRequest.getProcedureId())) {
                throw new ValidationException(
                        i18nService.getMessage(ErrorCode.PROCEDURE_NOT_FOUND.getMessageKey(),
                                agreementProcedureRequest.getProcedureId())
                );
            }
        });

        Map<UUID, BigDecimal> procedureCostMap = agreementRequest.getProcedures().stream()
                .collect(Collectors.toMap(AgreementProcedureRequest::getProcedureId,
                        AgreementProcedureRequest::getCost));
        List<AgreementProceduresDetail> proceduresDetails = toAgreementProceduresDetail(agreement, procedures,
                procedureCostMap);
        agreementProceduresDetailRepository.saveAll(proceduresDetails);
        agreement.setProceduresDetails(proceduresDetails);

        agreementRepository.saveAndFlush(agreement);
        return detail(agreementRequest.getId());
    }

    private AgreementDTO toAgreementDTO(Agreement agreement) {
        AgreementDTO agreementDTO = agreementMapper.toDTO(agreement);
        if (agreement.getProceduresDetails() != null) {
            agreementDTO.setProcedures(
                    agreement.getProceduresDetails().stream()
                            .map(procedureDetail -> {
                                AgreementProcedureDTO procedureDTO = new AgreementProcedureDTO();
                                procedureDTO.setProcedureId(procedureDetail.getId().getProcedureId());
                                procedureDTO.setCost(procedureDetail.getCost());
                                procedureDTO.setProcedureInfo(procedureMapper.toDTO(procedureDetail.getProcedure()));
                                return procedureDTO;
                            }).toList()
            );
        }
        if (agreement.getProvider() != null) {
            agreementDTO.setMedicalProvider(
                    medicalProviderMapper.toSimpleDTO(agreement.getProvider())
            );
        }
        return agreementDTO;
    }

    private Agreement toAgreement(CreateAgreementDTO agreement) {
        Agreement entity = agreementMapper.toEntity(agreement);
        return entity;
    }

    private List<AgreementProceduresDetail> toAgreementProceduresDetail(Agreement agreement,
                                                                        List<Procedure> procedures,
                                                                        Map<UUID, BigDecimal> cost) {
        return procedures.stream().map(procedure -> {
            AgreementProceduresDetail entity = new AgreementProceduresDetail();
            AgreementProceduresDetailId id = new AgreementProceduresDetailId();
            id.setAgreementId(agreement.getId());
            id.setProcedureId(procedure.getId());
            entity.setId(id);
            entity.setAgreement(agreement);
            entity.setProcedure(procedure);
            entity.setCost(cost.get(procedure.getId()));
            return entity;
        }).toList();

    }

}
