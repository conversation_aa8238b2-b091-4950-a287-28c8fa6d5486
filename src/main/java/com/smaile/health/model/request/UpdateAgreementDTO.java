package com.smaile.health.model.request;

import com.smaile.health.constants.Status;
import com.smaile.health.model.AgreementProcedureDTO;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

import java.time.Instant;
import java.util.List;
import java.util.UUID;

@Getter
@Setter
public class UpdateAgreementDTO {

    private UUID id;

    @NotNull(message = "Contract id is required")
    @NotEmpty
    private String contractId;

    @NotNull(message = "Short description is required")
    private String shortDescription;

    private String longDescription;

    @NotNull(message = "Effective time is required")
    private Instant effectiveTime;

    @NotNull(message = "Cancellation time is required")
    private Instant cancellationTime;

    @NotNull(message = "Status is required")
    private Status status;

}
