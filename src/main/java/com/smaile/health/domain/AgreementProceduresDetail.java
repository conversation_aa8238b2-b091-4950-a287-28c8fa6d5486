package com.smaile.health.domain;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.UUID;

@Entity
@Table(name = "agreement_procedure_details")
@Getter
@Setter
public class AgreementProceduresDetail extends BaseEntity {

    @EmbeddedId
    private AgreementProceduresDetailId id;

    @ManyToOne
    @JoinColumn(name="agreement_id", nullable=false)
    @MapsId("agreementId")
    private Agreement agreement;

    @ManyToOne
    @JoinColumn(name="procedure_id", nullable=false)
    @MapsId("procedureId")
    private Procedure procedure;

    @Column(nullable = false)
    private BigDecimal cost;
}
