package com.smaile.health.domain;

import com.smaile.health.constants.AgeGroup;
import com.smaile.health.constants.DiagnosisFrequency;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinTable;
import jakarta.persistence.ManyToMany;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.UUID;

@Entity
@Table(name = "procedure")
@Getter
@Setter
public class Procedure extends BaseEntity {

    @Id
    private UUID id;

    @Column(name = "code", nullable = false, unique = true)
    private String code; // ADA CDT code or custom country code

    @Enumerated(EnumType.STRING)
    @Column(name = "frequency", nullable = false)
    private DiagnosisFrequency frequency;

    @Column(name = "description", nullable = false, columnDefinition = "TEXT")
    private String description;

    @Enumerated(EnumType.STRING)
    @Column(name = "age_group", nullable = false)
    private AgeGroup ageGroup;

    @Column(name = "market_cost", nullable = false, precision = 10, scale = 2)
    private BigDecimal marketCost;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "mouth_part_requirements", columnDefinition = "JSON")
    private List<String> mouthPartRequirements;

    @ManyToMany(fetch = FetchType.EAGER)
    @JoinTable(
            name = "procedure_diagnosis",
            joinColumns = @JoinColumn(name = "procedure_id"),
            inverseJoinColumns = @JoinColumn(name = "diagnosis_id")
    )
    private List<Diagnosis> relatedDiagnoses;

    @Column(name = "effective_date", nullable = false)
    private LocalDate effectiveDate;

    @Column(name = "cancellation_date")
    private LocalDate cancellationDate;

    @ManyToMany(fetch = FetchType.EAGER)
    @JoinTable(
            name = "procedure_speciality",
            joinColumns = @JoinColumn(name = "procedure_id"),
            inverseJoinColumns = @JoinColumn(name = "speciality_id")
    )
    private List<Speciality> specialties;

    @ManyToMany(fetch = FetchType.EAGER)
    @JoinTable(
            name = "procedure_proof",
            joinColumns = @JoinColumn(name = "procedure_id"),
            inverseJoinColumns = @JoinColumn(name = "proof_id")
    )
    private List<Proof> proofs;

    @Column(name = "is_active", nullable = false)
    private Boolean isActive = true;
}
